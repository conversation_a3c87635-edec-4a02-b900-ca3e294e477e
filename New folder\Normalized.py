import numpy as np
import chess
import torch
import torch.nn as nn
import torch.optim as optim
import pandas as pd
from torch.utils.data import DataLoader, TensorDataset
from tqdm import tqdm
import matplotlib.pyplot as plt
import time
import os
import glob

# Mount Google Drive (Google Colab)


# Define the base directory for data and model files
DATA_DIR = 'trainCVS'
Test_DIR = 'test'
# Adjust these paths as needed for your environment.

# Set device to GPU if available
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print("Using device:", device)

# Convert a FEN string to a bitboard representation (12 pieces × 64 squares)
def fen_to_bitboard(fen):
    board = chess.Board(fen)
    bitboard = np.zeros(768)  # 12 pieces × 64 squares
    piece_map = {
        "P": 0, "N": 1, "B": 2, "R": 3, "Q": 4, "K": 5,
        "p": 6, "n": 7, "b": 8, "r": 9, "q": 10, "k": 11,
    }
    for square in chess.SQUARES:
        piece = board.piece_at(square)
        if piece:
            index = piece_map[piece.symbol()] * 64 + square
            bitboard[index] = 1
    return bitboard

# Example usage of fen_to_bitboard
print("Example bitboard from starting position:")
print(fen_to_bitboard("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"))

# Define the NNUE model with one hidden layer and clipped ReLU (using torch.clamp)
class NNUE(nn.Module):
    def __init__(self):
        super(NNUE, self).__init__()
        self.fc1 = nn.Linear(768, 512)
        self.fc2 = nn.Linear(512, 1)
        # Initialize with appropriate scaling for output in range [-1, 1]
        nn.init.xavier_uniform_(self.fc1.weight)
        nn.init.xavier_uniform_(self.fc2.weight)
        nn.init.zeros_(self.fc2.bias)
        
    def forward(self, x):
        # Clipped ReLU: clamp values between 0 and 1 (normalized)
        x = torch.clamp(self.fc1(x), 0, 1)
        # Final output with tanh to constrain values to [-1, 1] range
        return torch.tanh(self.fc2(x))

# Function to prompt user for model file path
def select_model():
    print("\n=== Model Selection ===")
    print("1. Load existing model (.pt file)")
    print("2. Start with a new model")
    
    while True:
        try:
            choice = int(input("\nEnter your choice (1 or 2): "))
            if choice == 2:
                print("Starting with a new model.")
                return None
            elif choice == 1:
                model_path = input("\nEnter the path to the .pt file to load (relative to Chessnnue folder, e.g., 'model.pt'): ")
                if not model_path.endswith('.pt'):
                    print("Warning: The file specified doesn't have a .pt extension.")
                    confirm = input("Continue anyway? (y/n): ")
                    if confirm.lower() != 'y':
                        continue
                if not os.path.isabs(model_path):
                    model_path = os.path.join(DATA_DIR, model_path)
                if not os.path.exists(model_path):
                    print(f"Error: File '{model_path}' does not exist.")
                    model_files = glob.glob(os.path.join(DATA_DIR, "*.pt"))
                    if model_files:
                        print("\nAvailable .pt files in the directory:")
                        for i, file in enumerate(model_files, 1):
                            print(f"  {i}. {os.path.basename(file)}")
                    continue
                return model_path
            else:
                print("Invalid choice. Please enter 1 or 2.")
        except ValueError:
            print("Please enter a valid number.")

# Helper function to safely load a model checkpoint
def safe_load_model(model_path):
    print(f"Loading model from {model_path}...")
    try:
        checkpoint = torch.load(model_path, map_location=device)
        if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
            return checkpoint['model_state_dict'], checkpoint.get('epoch', 0), checkpoint.get('loss_history', []), checkpoint.get('accuracy_history', [])
        else:
            return checkpoint, 0, [], []
    except Exception as e:
        print(f"Error loading model: {str(e)}")
        raise e

# Training function with autosave and live plotting, plus a scatter plot for validation
def train_nnue(model, X_train, y_train, X_val, y_val, epochs=10, batch_size=512,
               start_epoch=0, loss_history=None, accuracy_history=None, current_file=""):
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    loss_fn = nn.MSELoss()
    
    train_dataset = TensorDataset(X_train, y_train)
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    
    if loss_history is None:
        loss_history = []
    if accuracy_history is None:
        accuracy_history = []
    
    last_autosave_time = time.time()
    
    for epoch in range(start_epoch, start_epoch + epochs):
        model.train()
        epoch_loss = 0.0
        with tqdm(total=len(train_loader), desc=f"Epoch {epoch+1}", unit="batch") as pbar:
            for batch_x, batch_y in train_loader:
                batch_x, batch_y = batch_x.to(device), batch_y.to(device)
                optimizer.zero_grad()
                predictions = model(batch_x)
                loss = loss_fn(predictions, batch_y)
                loss.backward()
                optimizer.step()
                
                epoch_loss += loss.item()
                pbar.set_postfix({"loss": f"{loss.item():.4f}"})
                pbar.update(1)
                
                # Autosave weights every 20 minutes
                if time.time() - last_autosave_time >= 20 * 60:
                    autosave_filename = os.path.join(DATA_DIR, f"autosave_{int(time.time())}.pt")
                    # Save model with state_dict and metadata
                    torch.save({
                        'model_state_dict': model.state_dict(),
                        'epoch': epoch,
                        'loss_history': loss_history,
                        'accuracy_history': accuracy_history
                    }, autosave_filename)
                    print(f"\nAutosaved model weights for file '{current_file}' to {autosave_filename}")
                    last_autosave_time = time.time()
        
        avg_train_loss = epoch_loss / len(train_loader)
        loss_history.append(avg_train_loss)
        
        # Validation step
        model.eval()
        with torch.no_grad():
            val_predictions = model(X_val.to(device))
            val_loss = loss_fn(val_predictions, y_val.to(device)).item()
        
        # Compute accuracy based on normalized [-1, 1] range
        actual = y_val.cpu().numpy().flatten()
        predicted = val_predictions.cpu().numpy().flatten()
        
        # Calculate MAE relative to the full range of [-1, 1] (range = 2)
        # This gives us a percentage-like accuracy measure
        mae = np.mean(np.abs(predicted - actual))
        avg_accuracy = (1 - mae/2) * 100  # Normalize against full range of 2
        accuracy_history.append(avg_accuracy)
        
        print(f"\nEpoch {epoch+1} on '{current_file}' completed. "
              f"Avg Training Loss: {avg_train_loss:.4f}, "
              f"Validation Loss: {val_loss:.4f}, "
              f"Average Accuracy: {avg_accuracy:.2f}%")
        
        # -------------------------------------------------
        # 1) PLOT PREDICTED VS ACTUAL FOR VALIDATION
        # -------------------------------------------------
        plt.figure(figsize=(8, 6))
        plt.scatter(actual, predicted, s=10, color='blue', alpha=0.6, label='Predictions')
        
        # Plot the perfect prediction line
        plt.plot([-1, 1], [-1, 1], color='red', linestyle='--', label='Perfect Prediction')
        
        # Set fixed axis limits for consistency between epochs
        plt.xlim([-1, 1])
        plt.ylim([-1, 1])
        plt.xlabel('Actual Score')
        plt.ylabel('Predicted Score')
        plt.title(f"Epoch {epoch+1}: Validation\nTrain Loss: {avg_train_loss:.4f}, Val Loss: {val_loss:.4f}")
        plt.legend()
        plt.grid(True, linestyle='--', alpha=0.7)
        
        # Save scatter plot (valid1.png, valid2.png, etc.)
        valid_plot_filename = os.path.join(DATA_DIR, f"valid{epoch+1}.png")
        plt.savefig(valid_plot_filename)
        plt.close()
        
        # -------------------------------------------------
        # 2) PLOT TRAINING LOSS & ACCURACY
        # -------------------------------------------------
        epochs_range = range(1, len(loss_history) + 1)
        fig, ax1 = plt.subplots(figsize=(8, 6))
        color = 'tab:blue'
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Training Loss', color=color)
        ax1.plot(epochs_range, loss_history, color=color, marker='o', label='Training Loss')
        ax1.tick_params(axis='y', labelcolor=color)
        
        ax2 = ax1.twinx()
        color = 'tab:green'
        ax2.set_ylabel('Avg Accuracy (%)', color=color)
        ax2.plot(epochs_range, accuracy_history, color=color, marker='o', label='Avg Accuracy')
        ax2.tick_params(axis='y', labelcolor=color)
        ax2.set_ylim([0, 100])  # Fix accuracy scale from 0-100%
        
        plt.title(f"Training Progress on '{current_file}' (Epoch {epoch+1})")
        fig.tight_layout()
        
        # Add grid for better readability
        ax1.grid(True, linestyle='--', alpha=0.3)
        
        plot_filename = os.path.join(DATA_DIR, f"{os.path.splitext(current_file)[0]}_epoch{epoch+1:03d}.png")
        plt.savefig(plot_filename)
        plt.close()
    
    return loss_history, accuracy_history

if __name__ == "__main__":
    # Load test data from test01.csv
    test_csv_path = os.path.join(Test_DIR, "test01.csv")
    print("Loading test data...")
    test_df = pd.read_csv(test_csv_path)
    
    # Verify score range
    print(f"Score range in test data: [{test_df['Score'].min()}, {test_df['Score'].max()}]")
    if test_df['Score'].min() < -1 or test_df['Score'].max() > 1:
        print("WARNING: Scores outside [-1, 1] range detected. Normalizing...")
        # Normalize scores to [-1, 1] if they're outside that range
        max_abs_score = max(abs(test_df['Score'].min()), abs(test_df['Score'].max()))
        test_df['Score'] = test_df['Score'] / max_abs_score
        print(f"Normalized score range: [{test_df['Score'].min()}, {test_df['Score'].max()}]")
    
    y_test_np = np.array(test_df["Score"]).reshape(-1, 1)
    X_test_np = np.array([fen_to_bitboard(fen) for fen in tqdm(test_df["FEN"], desc="Processing test positions")])
    
    X_val = torch.tensor(X_test_np, dtype=torch.float32).to(device)
    y_val = torch.tensor(y_test_np, dtype=torch.float32).to(device)
    
    print(f"Test data: {len(X_val)} positions")
    
    # Prompt user to select an existing model or start fresh
    selected_model = select_model()
    model = NNUE().to(device)
    global_epoch = 0
    loss_history = []
    accuracy_history = []
    
    if selected_model:
        checkpoint, saved_epoch, saved_loss_history, saved_accuracy_history = safe_load_model(selected_model)
        try:
            if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
                model.load_state_dict(checkpoint['model_state_dict'])
                global_epoch = checkpoint.get('epoch', 0) + 1
                loss_history = checkpoint.get('loss_history', [])
                accuracy_history = checkpoint.get('accuracy_history', [])
            else:
                model.load_state_dict(checkpoint)
                global_epoch = saved_epoch + 1
                loss_history = saved_loss_history
                accuracy_history = saved_accuracy_history
            print(f"Successfully loaded model. Resuming training from global epoch {global_epoch}")
        except Exception as e:
            print(f"Error initializing model with loaded weights: {e}")
            print("Starting with a fresh model.")
            model = NNUE().to(device)
    
    # Ask for number of epochs to train per CSV file
    try:
        epochs = int(input("Enter number of epochs to train on each CSV file: "))
    except ValueError:
        print("Invalid input. Using default value of 10 epochs.")
        epochs = 10

    # Gather all CSV files in DATA_DIR (alphabetical order) and exclude 'test01.csv' and 'train.csv'
    train_files = sorted(glob.glob(os.path.join(DATA_DIR, "*.csv")))
    train_files = [f for f in train_files if os.path.basename(f) not in ["test01.csv", "train.csv"]]
    
    if not train_files:
        print("No training CSV files found in", DATA_DIR)
        exit(1)
    
    # Loop through each CSV file for training
    for csv_file in train_files:
        current_file = os.path.basename(csv_file)
        print(f"\n--- Training on file: {current_file} ---")
        train_df = pd.read_csv(csv_file)
        
        # Verify score range in training data
        print(f"Score range in {current_file}: [{train_df['Score'].min()}, {train_df['Score'].max()}]")
        if train_df['Score'].min() < -1 or train_df['Score'].max() > 1:
            print(f"WARNING: Scores in {current_file} outside [-1, 1] range. Normalizing...")
            # Normalize scores to [-1, 1] if they're outside that range
            max_abs_score = max(abs(train_df['Score'].min()), abs(train_df['Score'].max()))
            train_df['Score'] = train_df['Score'] / max_abs_score
            print(f"Normalized score range: [{train_df['Score'].min()}, {train_df['Score'].max()}]")
        
        y_train_np = np.array(train_df["Score"]).reshape(-1, 1)
        X_train_np = np.array([fen_to_bitboard(fen) for fen in tqdm(train_df["FEN"], desc=f"Processing positions in {current_file}")])
        
        # Convert training data to tensors and move to device
        X_train = torch.tensor(X_train_np, dtype=torch.float32).to(device)
        y_train = torch.tensor(y_train_np, dtype=torch.float32).to(device)
        
        print(f"Training data: {len(X_train)} positions from file '{current_file}'")
        
        # Train on the current CSV file
        loss_history, accuracy_history = train_nnue(
            model, X_train, y_train, X_val, y_val,
            epochs=epochs,
            batch_size=32,
            start_epoch=global_epoch,
            loss_history=loss_history,
            accuracy_history=accuracy_history,
            current_file=current_file
        )
        global_epoch += epochs

    # Save final model and training history
    final_save_path = os.path.join(DATA_DIR, f"nnue_weights_globalEpoch{global_epoch}.pt")
    # Save model with comprehensive metadata
    torch.save({
        'model_state_dict': model.state_dict(),
        'epoch': global_epoch - 1,
        'loss_history': loss_history,
        'accuracy_history': accuracy_history
    }, final_save_path)
    
    print(f"\nTraining complete on all files. Final model saved to {final_save_path}")
    print("Key normalizations applied:")
    print("1. Model outputs constrained to [-1, 1] range with tanh activation")
    print("2. ReLU activation normalized to [0, 1] range")
    print("3. Accuracy calculation adjusted for [-1, 1] score range")
    print("4. Auto-normalization of any training files with scores outside [-1, 1]")
    print("5. Consistent graph scales for better epoch-to-epoch comparison")