#
# AI vs. Stockfish Game Runner with Tkinter UI (Corrected Version)
#
# This script allows a user to pit a trained PyTorch Chess AI model
# against the Stockfish chess engine on a local PC.
#

import tkinter as tk
from tkinter import ttk, filedialog, scrolledtext
import threading
import queue
import os
from datetime import datetime
import random

# --- Core Chess and AI Libraries ---
import torch
import torch.nn as nn
import numpy as np
import chess
import chess.engine
import chess.pgn

# --- Global Device Configuration ---
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# ==============================================================================
# 1. AI MODEL AND DATA PROCESSOR DEFINITIONS
# ==============================================================================

class ChessDataProcessor:
    """Handles chess data processing and board representation."""
    def board_to_tensor(self, board):
        tensor = np.zeros((12, 8, 8), dtype=np.float32)
        piece_channels = {
            chess.PAWN: 0, chess.KNIGHT: 1, chess.BISHOP: 2,
            chess.ROOK: 3, chess.QUEEN: 4, chess.KING: 5
        }
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece:
                row, col = divmod(square, 8)
                channel = piece_channels[piece.piece_type]
                if piece.color == chess.BLACK:
                    channel += 6
                tensor[channel, row, col] = 1.0
        return tensor

    def get_game_features(self, board):
        features = np.zeros(8, dtype=np.float32)
        features[0] = float(board.turn)
        features[1] = float(board.has_kingside_castling_rights(chess.WHITE))
        features[2] = float(board.has_queenside_castling_rights(chess.WHITE))
        features[3] = float(board.has_kingside_castling_rights(chess.BLACK))
        features[4] = float(board.has_queenside_castling_rights(chess.BLACK))
        features[5] = float(len(list(board.legal_moves))) / 50.0
        features[6] = float(board.is_check())
        features[7] = float(board.fullmove_number) / 100.0
        return features

class ChessCNN(nn.Module):
    """
    CORRECTED CNN Model definition.
    This architecture MUST EXACTLY MATCH the one used for training.
    """
    def __init__(self, input_channels=12, feature_dim=8):
        super(ChessCNN, self).__init__()
        
        # Convolutional Head - This now matches the Colab script with 3 conv layers
        self.conv_head = nn.Sequential(
            nn.Conv2d(input_channels, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(),
            nn.Conv2d(64, 128, kernel_size=3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(),
            # This was the missing part
            nn.Conv2d(128, 128, kernel_size=3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU()
        )
        
        self.residual_blocks = nn.ModuleList([self._make_residual_block(128) for _ in range(4)])
        self.pool = nn.AdaptiveAvgPool2d((1, 1))
        
        self.fc_head = nn.Sequential(
            nn.Linear(128 + feature_dim, 512), nn.ReLU(), nn.Dropout(0.3),
            nn.Linear(512, 256), nn.ReLU(), nn.Dropout(0.2),
            nn.Linear(256, 1), nn.Tanh()
        )
        
    def _make_residual_block(self, channels):
        return nn.Sequential(
            nn.Conv2d(channels, channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(channels),
            nn.ReLU(),
            nn.Conv2d(channels, channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(channels)
        )
        
    def forward(self, board_tensor, feature_tensor):
        x = self.conv_head(board_tensor)
        for block in self.residual_blocks:
            residual = x
            x = block(x)
            x += residual
            x = nn.functional.relu(x)
        x = self.pool(x)
        x = torch.flatten(x, 1)
        combined = torch.cat([x, feature_tensor], dim=1)
        return self.fc_head(combined)

# ==============================================================================
# 2. AI ENGINE WRAPPER
# ==============================================================================

class AIEngine:
    """A wrapper for the PyTorch model to make it act like a chess engine."""
    def __init__(self, checkpoint_path, ai_color):
        self.processor = ChessDataProcessor()
        self.model = ChessCNN().to(DEVICE)
        
        try:
            checkpoint = torch.load(checkpoint_path, map_location=DEVICE)
            self.model.load_state_dict(checkpoint['model_state_dict'])
        except Exception as e:
            raise IOError(f"Failed to load checkpoint. Error: {e}") from e

        self.model.eval()
        self.color = ai_color

    def get_best_move(self, board):
        legal_moves = list(board.legal_moves)
        if not legal_moves: return None
        best_move = random.choice(legal_moves) # Fallback move
        
        if self.color == chess.WHITE:
            best_score = -float('inf')
        else:
            best_score = float('inf')

        with torch.no_grad():
            for move in legal_moves:
                board.push(move)
                
                board_tensor = self.processor.board_to_tensor(board)
                features = self.processor.get_game_features(board)
                board_t = torch.from_numpy(np.expand_dims(board_tensor, 0)).to(DEVICE)
                feat_t = torch.from_numpy(np.expand_dims(features, 0)).to(DEVICE)
                
                score = self.model(board_t, feat_t).item()
                
                board.pop()

                if self.color == chess.WHITE:
                    if score > best_score:
                        best_score = score
                        best_move = move
                else:
                    if score < best_score:
                        best_score = score
                        best_move = move
                        
        return best_move

# ==============================================================================
# 3. TKINTER APPLICATION
# ==============================================================================

class Application(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("AI vs. Stockfish")
        self.geometry("600x650")

        self.log_queue = queue.Queue()
        self.create_widgets()
        self.process_log_queue()

    def create_widgets(self):
        main_frame = ttk.Frame(self, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        file_frame = ttk.LabelFrame(main_frame, text="Engine and Model Setup", padding="10")
        file_frame.pack(fill=tk.X, pady=5)

        ttk.Label(file_frame, text="Stockfish Executable:").grid(row=0, column=0, sticky="w", pady=2)
        self.stockfish_path_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.stockfish_path_var, width=50).grid(row=0, column=1, sticky="ew")
        ttk.Button(file_frame, text="Browse...", command=self._browse_stockfish).grid(row=0, column=2, padx=5)

        ttk.Label(file_frame, text="AI Checkpoint (.pt):").grid(row=1, column=0, sticky="w", pady=2)
        self.checkpoint_path_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.checkpoint_path_var, width=50).grid(row=1, column=1, sticky="ew")
        ttk.Button(file_frame, text="Browse...", command=self._browse_checkpoint).grid(row=1, column=2, padx=5)
        
        file_frame.columnconfigure(1, weight=1)

        config_frame = ttk.LabelFrame(main_frame, text="Game Configuration", padding="10")
        config_frame.pack(fill=tk.X, pady=5)

        ttk.Label(config_frame, text="AI Plays As:").grid(row=0, column=0, sticky="w")
        self.ai_color_var = tk.StringVar(value="White")
        ttk.Combobox(config_frame, textvariable=self.ai_color_var, values=["White", "Black"], state="readonly").grid(row=0, column=1, sticky="w")

        ttk.Label(config_frame, text="Stockfish Skill Level (1-20):").grid(row=1, column=0, sticky="w")
        self.skill_level_var = tk.IntVar(value=10)
        scale = ttk.Scale(config_frame, from_=1, to=20, variable=self.skill_level_var, orient='horizontal', command=lambda s:self.skill_level_var.set(int(float(s))))
        scale.grid(row=1, column=1, sticky="ew")
        
        config_frame.columnconfigure(1, weight=1)

        self.start_button = ttk.Button(main_frame, text="Start Game", command=self._start_game)
        self.start_button.pack(pady=10, fill=tk.X)

        log_frame = ttk.LabelFrame(main_frame, text="Game Log", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        self.log_text = scrolledtext.ScrolledText(log_frame, state='disabled', wrap=tk.WORD, height=15, bg="white", fg="black")
        self.log_text.pack(fill=tk.BOTH, expand=True)

    def _browse_stockfish(self):
        path = filedialog.askopenfilename(title="Select Stockfish Executable")
        if path:
            self.stockfish_path_var.set(path)

    def _browse_checkpoint(self):
        path = filedialog.askopenfilename(title="Select AI Checkpoint File", filetypes=[("PyTorch Checkpoints", "*.pt")])
        if path:
            self.checkpoint_path_var.set(path)

    def _log(self, message):
        self.log_queue.put(message)

    def process_log_queue(self):
        try:
            while True:
                message = self.log_queue.get_nowait()
                self.log_text.config(state='normal')
                self.log_text.insert(tk.END, message + "\n")
                self.log_text.see(tk.END)
                self.log_text.config(state='disabled')
        except queue.Empty:
            pass
        self.after(100, self.process_log_queue)

    def _start_game(self):
        stockfish_path = self.stockfish_path_var.get()
        checkpoint_path = self.checkpoint_path_var.get()

        if not os.path.exists(stockfish_path):
            self._log("Error: Stockfish path is invalid.")
            return
        if not os.path.exists(checkpoint_path):
            self._log("Error: AI checkpoint path is invalid.")
            return

        self.start_button.config(state='disabled', text="Game in Progress...")
        self.log_text.config(state='normal')
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state='disabled')

        game_thread = threading.Thread(
            target=self._run_game_thread,
            args=(stockfish_path, checkpoint_path, self.ai_color_var.get(), self.skill_level_var.get()),
            daemon=True
        )
        game_thread.start()

    def _run_game_thread(self, stockfish_path, checkpoint_path, ai_color_str, skill_level):
        try:
            ai_color = chess.WHITE if ai_color_str == "White" else chess.BLACK
            self._log(f"Initializing AI Engine ({ai_color_str}) from: {os.path.basename(checkpoint_path)}")
            self._log(f"Using device: {DEVICE}")
            ai_engine = AIEngine(checkpoint_path, ai_color)

            self._log(f"Initializing Stockfish Engine (Skill Level: {skill_level})")
            stockfish_engine = chess.engine.SimpleEngine.popen_uci(stockfish_path)
            stockfish_engine.configure({"Skill Level": skill_level})

            board = chess.Board()
            game = chess.pgn.Game()
            game.headers["Event"] = "AI vs. Stockfish"
            game.headers["Site"] = "Local PC"
            game.headers["Date"] = datetime.now().strftime("%Y.%m.%d")
            game.headers["White"] = "AI" if ai_color == chess.WHITE else "Stockfish"
            game.headers["Black"] = "AI" if ai_color == chess.BLACK else "Stockfish"
            node = game

            self._log("\n--- Game Started! ---\n")
            
            while not board.is_game_over(claim_draw=True):
                move_num_str = f"{board.fullmove_number}. " if board.turn == chess.WHITE else f"{board.fullmove_number}... "
                
                if board.turn == ai_color:
                    self._log(f"{move_num_str}AI is thinking...")
                    move = ai_engine.get_best_move(board)
                else:
                    self._log(f"{move_num_str}Stockfish is thinking...")
                    result = stockfish_engine.play(board, chess.engine.Limit(time=1.0)) 
                    move = result.move

                move_san = board.san(move)
                self._log(f"-> Move Played: {move_san}")
                board.push(move)
                node = node.add_variation(move)

            self._log("\n--- Game Over! ---")
            self._log(f"Result: {board.result(claim_draw=True)}")

            if not os.path.exists("games"):
                os.makedirs("games")
            pgn_filename = f"games/game_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pgn"
            with open(pgn_filename, "w", encoding="utf-8") as f:
                f.write(str(game))
            self._log(f"Game saved to: {pgn_filename}")
            
            stockfish_engine.quit()

        except Exception as e:
            self._log(f"\n--- An Error Occurred ---")
            self._log(str(e))
        finally:
            self.start_button.config(state='normal', text="Start Game")

# ==============================================================================
# 4. MAIN EXECUTION BLOCK
# ==============================================================================
if __name__ == "__main__":
    app = Application()
    app.mainloop()