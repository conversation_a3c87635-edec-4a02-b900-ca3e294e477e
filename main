import numpy as np
import chess

import torch
import torch.nn as nn
import torch.optim as optim

import pandas as pd
from torch.utils.data import DataLoader, TensorDataset

from tqdm import tqdm
import matplotlib.pyplot as plt
import time
import os
import glob

# Convert a FEN string to a bitboard representation (12 pieces × 64 squares)
def fen_to_bitboard(fen):
    board = chess.Board(fen)
    bitboard = np.zeros(768)  # 12 pieces × 64 squares
    piece_map = {
        "P": 0, "N": 1, "B": 2, "R": 3, "Q": 4, "K": 5,
        "p": 6, "n": 7, "b": 8, "r": 9, "q": 10, "k": 11,
    }
    for square in chess.SQUARES:
        piece = board.piece_at(square)
        if piece:
            index = piece_map[piece.symbol()] * 64 + square
            bitboard[index] = 1
    return bitboard

# Example usage of fen_to_bitboard
print(fen_to_bitboard("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"))

# Define the optimized NNUE model with a single hidden layer and clipped ReLU (ReLU6)
class NNUE(nn.Module):
    def __init__(self):
        super(NNUE, self).__init__()
        self.fc1 = nn.Linear(768, 512)
        self.fc2 = nn.Linear(512, 1)
        
    def forward(self, x):
        # Using clipped ReLU: clamp values between 0 and 6 for numerical stability
        x = torch.clamp(self.fc1(x), 0, 6)
        return self.fc2(x)

# Function to prompt user for model file path
def select_model():
    print("\n=== Model Selection ===")
    print("1. Load existing model (.pt file)")
    print("2. Start with a new model")
    
    while True:
        try:
            choice = int(input("\nEnter your choice (1 or 2): "))
            if choice == 2:
                print("Starting with a new model.")
                return None
            elif choice == 1:
                # Let user enter the exact path to the model file
                model_path = input("\nEnter the path to the .pt file to load (e.g., 'model.pt' or '/path/to/model.pt'): ")
                
                if not model_path.endswith('.pt'):
                    print("Warning: The file specified doesn't have a .pt extension.")
                    confirm = input("Continue anyway? (y/n): ")
                    if confirm.lower() != 'y':
                        continue
                
                if not os.path.exists(model_path):
                    print(f"Error: File '{model_path}' does not exist.")
                    # Show available models as a helpful suggestion
                    model_files = glob.glob("*.pt")
                    if model_files:
                        print("\nAvailable .pt files in current directory:")
                        for i, file in enumerate(model_files, 1):
                            print(f"  {i}. {file}")
                    continue
                
                return model_path
            else:
                print("Invalid choice. Please enter 1 or 2.")
        except ValueError:
            print("Please enter a valid number.")

# Training function with validation, graph plotting, normalized accuracy computation,
# and autosave of weights every 20 minutes.
def train_nnue(model, X_train, y_train, X_val, y_val, epochs=10, batch_size=32,
               start_epoch=0, loss_history=None, accuracy_history=None):
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    loss_fn = nn.MSELoss()
    
    train_dataset = TensorDataset(X_train, y_train)
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    
    # Initialize history lists if not provided
    if loss_history is None:
        loss_history = []
    if accuracy_history is None:
        accuracy_history = []
    
    last_autosave_time = time.time()  # Track last autosave time
    
    for epoch in range(start_epoch, start_epoch + epochs):
        model.train()
        epoch_loss = 0.0
        with tqdm(total=len(train_loader), desc=f"Epoch {epoch+1}/{start_epoch+epochs}", unit="batch") as pbar:
            for batch_x, batch_y in train_loader:
                optimizer.zero_grad()
                predictions = model(batch_x)
                loss = loss_fn(predictions, batch_y)
                loss.backward()
                optimizer.step()
                
                epoch_loss += loss.item()
                pbar.set_postfix({"loss": f"{loss.item():.4f}"})
                pbar.update(1)
                
                # Autosave weights every 20 minutes
                if time.time() - last_autosave_time >= 20 * 60:
                    autosave_filename = f"autosave_{int(time.time())}.pt"
                    # Save model weights and training state - store only the state_dict to avoid pickle issues
                    torch.save(model.state_dict(), autosave_filename)
                    
                    # Save history separately as numpy arrays to avoid pickle issues
                    np.savez(
                        autosave_filename.replace(".pt", "_history.npz"),
                        epoch=epoch,
                        loss_history=np.array(loss_history),
                        accuracy_history=np.array(accuracy_history)
                    )
                    
                    print(f"\nAutosaved model weights to {autosave_filename}")
                    print(f"Autosaved training history to {autosave_filename.replace('.pt', '_history.npz')}")
                    
                    last_autosave_time = time.time()
        
        avg_train_loss = epoch_loss / len(train_loader)
        loss_history.append(avg_train_loss)
        
        # Validation step
        model.eval()
        with torch.no_grad():
            val_predictions = model(X_val)
            val_loss = loss_fn(val_predictions, y_val).item()
        
        # Compute normalized average accuracy assuming scores are in [0,1]:
        # Accuracy = (1 - MAE) * 100, where MAE = mean(|predicted - actual|)
        actual = y_val.numpy().flatten()
        predicted = val_predictions.numpy().flatten()
        mae = np.mean(np.abs(predicted - actual))
        avg_accuracy = max(0, (1 - mae) * 100)
        accuracy_history.append(avg_accuracy)
        
        # Print metrics in the terminal
        print(f"\nEpoch {epoch+1} completed. Average Training Loss: {avg_train_loss:.4f}, "
              f"Validation Loss: {val_loss:.4f}, Average Accuracy: {avg_accuracy:.2f}%")
        
        # Plot metrics over epochs: training loss and average accuracy
        epochs_range = range(1, len(loss_history) + 1)
        fig, ax1 = plt.subplots(figsize=(8, 6))
        
        color = 'tab:blue'
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Training Loss', color=color)
        ax1.plot(epochs_range, loss_history, color=color, marker='o', label='Training Loss')
        ax1.tick_params(axis='y', labelcolor=color)
        
        ax2 = ax1.twinx()
        color = 'tab:green'
        ax2.set_ylabel('Average Accuracy (%)', color=color)
        ax2.plot(epochs_range, accuracy_history, color=color, marker='o', label='Avg Accuracy')
        ax2.tick_params(axis='y', labelcolor=color)
        
        plt.title(f"Training Progress: Loss & Accuracy (Epoch {epoch+1})")
        fig.tight_layout()
        
        # Save the plot as test001.png, test002.png, etc.
        filename = f"test{epoch+1:03d}.png"
        plt.savefig(filename)
        plt.close()
    
    return loss_history, accuracy_history

# Helper function to safely load a model
def safe_load_model(model_path):
    """Safely load a model with appropriate error handling for PyTorch compatibility"""
    print(f"Loading model from {model_path}...")
    try:
        # First try with weights_only=False
        state_dict = torch.load(model_path, weights_only=False)
        return state_dict['model_state_dict'], state_dict['epoch'], state_dict['loss_history'], state_dict['accuracy_history']
    except Exception as e:
        print(f"Error loading model: {str(e)}")
        print("Attempting alternative loading method...")
        try:
            # Add safe globals for numpy scalar
            torch.serialization.add_safe_globals(['numpy._core.multiarray.scalar'])
            state_dict = torch.load(model_path, weights_only=True)
            return state_dict['model_state_dict'], state_dict['epoch'], state_dict['loss_history'], state_dict['accuracy_history']
        except Exception as e:
            print(f"Failed to load model: {str(e)}")
            raise

# Main execution
if __name__ == "__main__":
    # Load the training dataset
    print("Loading training data...")
    train_df = pd.read_csv("train.csv")
    y_train_np = np.array(train_df["Score"]).reshape(-1, 1)
    X_train_np = np.array([fen_to_bitboard(fen) for fen in tqdm(train_df["FEN"], desc="Processing training positions")])
    
    # Load the test dataset
    print("Loading test data...")
    test_df = pd.read_csv("test01.csv")
    y_test_np = np.array(test_df["Score"]).reshape(-1, 1)
    X_test_np = np.array([fen_to_bitboard(fen) for fen in tqdm(test_df["FEN"], desc="Processing test positions")])
    
    # Convert numpy arrays to PyTorch tensors
    X_train = torch.tensor(X_train_np, dtype=torch.float32)
    y_train = torch.tensor(y_train_np, dtype=torch.float32)
    X_val = torch.tensor(X_test_np, dtype=torch.float32)
    y_val = torch.tensor(y_test_np, dtype=torch.float32)
    
    print(f"Training data: {len(X_train)} positions")
    print(f"Test data: {len(X_val)} positions")
    
    # Prompt user to select a model
    selected_model = select_model()
    
    # Initialize model and training state
    model = NNUE()
    start_epoch = 0
    loss_history = []
    accuracy_history = []
    
    # Load model if selected
    if selected_model:
        checkpoint, saved_epoch, saved_loss_history, saved_accuracy_history = safe_load_model(selected_model)
        
        if checkpoint is not None:
            try:
                # Check if it's a state_dict or a dictionary containing state_dict
                if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
                    # Full checkpoint with training state
                    model.load_state_dict(checkpoint['model_state_dict'])
                    start_epoch = checkpoint.get('epoch', 0) + 1
                    loss_history = checkpoint.get('loss_history', [])
                    accuracy_history = checkpoint.get('accuracy_history', [])
                else:
                    # Direct state dict
                    model.load_state_dict(checkpoint)
                    
                    # Use saved history if available
                    if saved_epoch is not None:
                        start_epoch = saved_epoch + 1
                    if saved_loss_history:
                        loss_history = saved_loss_history
                    if saved_accuracy_history:
                        accuracy_history = saved_accuracy_history
                
                print(f"Successfully loaded model. Resuming training from epoch {start_epoch}")
                
            except Exception as e:
                print(f"Error initializing model with loaded weights: {e}")
                print("Starting with a fresh model.")
                model = NNUE()
        else:
            print("Starting with a fresh model.")
    
    # Ask for number of epochs to train
    try:
        epochs = int(input("Enter number of epochs to train: "))
    except ValueError:
        print("Invalid input. Using default value of 10 epochs.")
        epochs = 10
    
    # Train the model
    loss_history, accuracy_history = train_nnue(
        model, X_train, y_train, X_val, y_val,
        epochs=epochs, 
        start_epoch=start_epoch,
        loss_history=loss_history,
        accuracy_history=accuracy_history
    )
    
    # Save final model with training state - avoid using pickle for compatibility
    final_save_path = f"nnue_weights_epoch{start_epoch+epochs}.pt"
    torch.save(model.state_dict(), final_save_path)
    
    # Save history separately as numpy arrays
    history_save_path = final_save_path.replace(".pt", "_history.npz")
    np.savez(
        history_save_path,
        epoch=start_epoch + epochs - 1,
        loss_history=np.array(loss_history),
        accuracy_history=np.array(accuracy_history)
    )
    
    print(f"Training complete. Final model saved to {final_save_path}")
    print(f"Training history saved to {history_save_path}")
