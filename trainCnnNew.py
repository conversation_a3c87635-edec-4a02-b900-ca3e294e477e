import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import chess
import chess.pgn
import io
import os
import zipfile
import json
import pickle
import random
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
import gc
import threading
import time
from collections import deque
import warnings
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg


warnings.filterwarnings('ignore')

# --- Global Device Configuration ---
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"--- Using device: {DEVICE} ---")


class ChessDataProcessor:
    """Handles chess data processing and board representation"""

    def __init__(self):
        # This class remains largely the same as it deals with chess logic, not ML frameworks.
        self.piece_to_int = {
            'P': 1, 'N': 2, 'B': 3, 'R': 4, 'Q': 5, 'K': 6,  # White pieces
            'p': -1, 'n': -2, 'b': -3, 'r': -4, 'q': -5, 'k': -6,  # Black pieces
            '.': 0  # Empty square
        }

    def board_to_tensor(self, board):
        """Convert chess board to 12x8x8 tensor representation for PyTorch (C, H, W)"""
        tensor = np.zeros((12, 8, 8), dtype=np.float32)

        piece_channels = {
            chess.PAWN: 0, chess.KNIGHT: 1, chess.BISHOP: 2,
            chess.ROOK: 3, chess.QUEEN: 4, chess.KING: 5
        }

        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece:
                row, col = divmod(square, 8)
                channel = piece_channels[piece.piece_type]
                if piece.color == chess.BLACK:
                    channel += 6
                tensor[channel, row, col] = 1.0 # PyTorch uses (C, H, W) format

        return tensor

    def get_game_features(self, board):
        """Extract additional game features"""
        features = np.zeros(8, dtype=np.float32)
        features[0] = float(board.turn)
        features[1] = float(board.has_kingside_castling_rights(chess.WHITE))
        features[2] = float(board.has_queenside_castling_rights(chess.WHITE))
        features[3] = float(board.has_kingside_castling_rights(chess.BLACK))
        features[4] = float(board.has_queenside_castling_rights(chess.BLACK))
        features[5] = float(len(list(board.legal_moves))) / 50.0
        features[6] = float(board.is_check())
        features[7] = float(board.fullmove_number) / 100.0

        return features

    def evaluate_position(self, board):
        """Simple position evaluation (-1 to 1)"""
        if board.is_checkmate():
            return -1.0 if board.turn else 1.0
        if board.is_stalemate() or board.is_insufficient_material():
            return 0.0

        piece_values = {
            chess.PAWN: 1, chess.KNIGHT: 3, chess.BISHOP: 3,
            chess.ROOK: 5, chess.QUEEN: 9, chess.KING: 0
        }

        white_material = sum(len(board.pieces(pt, chess.WHITE)) * val for pt, val in piece_values.items())
        black_material = sum(len(board.pieces(pt, chess.BLACK)) * val for pt, val in piece_values.items())

        material_diff = white_material - black_material
        total_material = white_material + black_material

        if total_material == 0: return 0.0
        return np.tanh(material_diff / 10.0)

class ChessCNN(nn.Module):
    """CNN Model for chess position evaluation using PyTorch"""

    def __init__(self, input_channels=12, feature_dim=8):
        super(ChessCNN, self).__init__()

        # Convolutional Head
        self.conv_head = nn.Sequential(
            nn.Conv2d(input_channels, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(),
            nn.Conv2d(64, 128, kernel_size=3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(),
            nn.Conv2d(128, 128, kernel_size=3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU()
        )

        # Residual Blocks
        self.residual_blocks = nn.ModuleList([self._make_residual_block(128) for _ in range(4)])

        self.pool = nn.AdaptiveAvgPool2d((1, 1))

        # Fully Connected Head
        self.fc_head = nn.Sequential(
            nn.Linear(128 + feature_dim, 512), # 128 from conv, feature_dim from game features
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 1),
            nn.Tanh() # Output evaluation between -1 and 1
        )

    def _make_residual_block(self, channels):
        return nn.Sequential(
            nn.Conv2d(channels, channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(channels),
            nn.ReLU(),
            nn.Conv2d(channels, channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(channels)
        )

    def forward(self, board_tensor, feature_tensor):
        # Process board tensor
        x = self.conv_head(board_tensor)

        # Residual connections
        for block in self.residual_blocks:
            residual = x
            x = block(x)
            x += residual
            x = nn.functional.relu(x)

        x = self.pool(x)
        x = torch.flatten(x, 1) # Flatten for FC layers

        # Combine with feature tensor
        combined = torch.cat([x, feature_tensor], dim=1)

        # Final evaluation
        evaluation = self.fc_head(combined)
        return evaluation

    def predict_evaluation(self, board_tensor, features):
        self.eval() # Set model to evaluation mode
        with torch.no_grad():
            board_t = torch.from_numpy(np.expand_dims(board_tensor, 0)).to(DEVICE)
            feat_t = torch.from_numpy(np.expand_dims(features, 0)).to(DEVICE)
            prediction = self(board_t, feat_t)
        return prediction.item()


class ChessTrainer:
    """Main training class adapted for PyTorch with automated training capabilities"""

    def __init__(self):
        self.data_processor = ChessDataProcessor()
        self.model = ChessCNN().to(DEVICE)
        self.optimizer = optim.Adam(self.model.parameters(), lr=0.001)
        self.criterion = nn.MSELoss()

        self.directories_set = False
        self.training_data = deque(maxlen=100000) # Memory for holding training chunks
        self.batch_size = 2048
        self.checkpoint_counter = 0
        self.history = {'loss': [], 'val_loss': [], 'accuracy': [], 'val_accuracy': []}

        # --- NEW: State Management Attributes ---
        self.training_state = {'processed_pgn_files': [], 'current_pgn_progress': {}}
        self.checkpoint_dir = ''
        self.pgn_dir = ''
        self.training_state_path = ''

    def set_directories(self, pgn_dir, checkpoint_dir):
        self.pgn_dir = pgn_dir
        self.checkpoint_dir = checkpoint_dir
        self.training_state_path = os.path.join(self.checkpoint_dir, 'training_state.json')
        os.makedirs(self.checkpoint_dir, exist_ok=True)
        os.makedirs(self.pgn_dir, exist_ok=True)
        self.directories_set = True
        print("✅ Directories set successfully")
        return True

    # --- NEW: Methods for saving and loading the overall training progress ---
    def save_training_state(self):
        """Saves the current training progress to a JSON file."""
        if not self.directories_set: return
        try:
            with open(self.training_state_path, 'w') as f:
                json.dump(self.training_state, f, indent=4)
            # A subtle print to avoid cluttering the output during automated loops
            # print("💾 Training state saved.")
        except Exception as e:
            print(f"❌ Error saving training state: {e}")

    def load_training_state(self):
        """Loads training progress from a JSON file, allowing resumption."""
        if not self.directories_set: return
        try:
            if os.path.exists(self.training_state_path):
                with open(self.training_state_path, 'r') as f:
                    self.training_state = json.load(f)
                    # Ensure keys exist for robustness
                    if 'processed_pgn_files' not in self.training_state:
                        self.training_state['processed_pgn_files'] = []
                    if 'current_pgn_progress' not in self.training_state:
                        self.training_state['current_pgn_progress'] = {}
                    print(f"✅ Training state loaded: {self.training_state}")
            else:
                print("No existing training state found. Starting fresh.")
        except Exception as e:
            print(f"❌ Could not load training state, starting fresh. Error: {e}")
            self.training_state = {'processed_pgn_files': [], 'current_pgn_progress': {}}

    # This method is kept for the manual "Load PGN" button
    def load_pgn_files(self, max_games_per_file=1000):
        # ... (This method remains unchanged for manual data loading)
        pass # The original code is fine here

    def _calculate_accuracy(self, predictions, labels):
        # ... (This method remains unchanged)
        pass # The original code is fine here

    def train_model(self, epochs=10):
        if len(self.training_data) < self.batch_size:
            print(f"❌ Not enough training data: {len(self.training_data)} positions")
            return

        print(f"🚀 Starting training for {epochs} epochs on {DEVICE}...")
        # ... (The rest of this method remains unchanged)
        pass # The original code is fine here

    def save_checkpoint(self, checkpoint_name=None, is_auto=False):
        if not self.directories_set:
            print("❌ Directories not set"); return False

        if checkpoint_name is None:
            checkpoint_name = f"manual_checkpoint_{datetime.now().strftime('%Y%m%d_%H%M')}"
        elif is_auto:
            # Automatic checkpoints are named consistently for easy identification
            checkpoint_name = f"auto_checkpoint_{datetime.now().strftime('%Y%m%d_%H%M')}"

        save_dir = os.path.join(self.checkpoint_dir, checkpoint_name)
        os.makedirs(save_dir, exist_ok=True)
        model_path = os.path.join(save_dir, 'model.pt')

        try:
            torch.save({
                'epoch': len(self.history['loss']),
                'model_state_dict': self.model.state_dict(),
                'optimizer_state_dict': self.optimizer.state_dict(),
                'history': self.history,
                'checkpoint_counter': self.checkpoint_counter,
                'batch_size': self.batch_size,
            }, model_path)

            self.checkpoint_counter += 1
            print(f"✅ Checkpoint saved: {checkpoint_name}/model.pt")
            return True

        except Exception as e:
            print(f"❌ Error saving checkpoint: {e}")
            return False

    def load_checkpoint(self, checkpoint_name):
        if not self.directories_set: return False

        load_path = os.path.join(self.checkpoint_dir, checkpoint_name, 'model.pt')
        if not os.path.exists(load_path):
            print(f"❌ Checkpoint not found: {load_path}"); return False

        try:
            checkpoint = torch.load(load_path, map_location=DEVICE)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            self.history = checkpoint.get('history', self.history)
            self.checkpoint_counter = checkpoint.get('checkpoint_counter', 0)
            self.batch_size = checkpoint.get('batch_size', 64)

            self.model.to(DEVICE)
            # --- NEW: Load training state along with the model ---
            self.load_training_state()
            print(f"✅ PyTorch Checkpoint loaded: {checkpoint_name}")
            return True

        except Exception as e:
            print(f"❌ Error loading checkpoint: {e}")
            return False

    # --- NEW: The core automated training pipeline ---
    def start_automated_training(self, max_games_per_chunk=5000, epochs_per_chunk=5):
        """
        Main automated training loop. Iterates through all PGN files, processes them
        in chunks, and saves progress automatically.
        """
        if not self.directories_set: return

        self.load_training_state()
        all_pgn_files = sorted([f for f in os.listdir(self.pgn_dir) if f.endswith('.pgn')])

        for pgn_filename in all_pgn_files:
            if pgn_filename in self.training_state['processed_pgn_files']:
                print(f"⏩ Skipping already completed file: {pgn_filename}")
                continue

            print(f"\n--- 📂 Processing file: {pgn_filename} ---")
            file_path = os.path.join(self.pgn_dir, pgn_filename)

            games_to_skip = 0
            progress = self.training_state.get('current_pgn_progress', {})
            if progress and progress.get('filename') == pgn_filename:
                games_to_skip = progress.get('games_processed', 0)
                print(f"🔄 Resuming from game number {games_to_skip + 1}")

            total_games_processed_in_file = games_to_skip

            while True: # Loop to process the current file in chunks
                self.training_data.clear()
                gc.collect()

                print(f"📖 Loading chunk from '{pgn_filename}' (skipping first {total_games_processed_in_file} games)...")
                games_loaded_in_chunk = self._load_pgn_chunk(
                    file_path,
                    games_to_skip=total_games_processed_in_file,
                    max_games_to_load=max_games_per_chunk
                )

                if games_loaded_in_chunk == 0:
                    print(f"✅ Finished processing all games in {pgn_filename}.")
                    self.training_state['processed_pgn_files'].append(pgn_filename)
                    # Clear progress if we just finished the file we were working on
                    if self.training_state.get('current_pgn_progress', {}).get('filename') == pgn_filename:
                        self.training_state['current_pgn_progress'] = {}
                    self.save_training_state()
                    break # Move to the next PGN file

                print(f"📊 Loaded {games_loaded_in_chunk} games. Total positions in memory: {len(self.training_data)}")

                self.train_model(epochs=epochs_per_chunk)

                total_games_processed_in_file += games_loaded_in_chunk

                # Update and save state after each successful training cycle
                self.training_state['current_pgn_progress'] = {
                    'filename': pgn_filename,
                    'games_processed': total_games_processed_in_file
                }
                self.save_checkpoint(is_auto=True)
                self.save_training_state()

        print("\n🎉🎉🎉 All available PGN files have been processed! 🎉🎉🎉")

    def _load_pgn_chunk(self, file_path, games_to_skip, max_games_to_load):
        """Helper to load a chunk of games from a PGN, skipping N games first."""
        games_loaded_this_chunk = 0
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                # Efficiently skip the games we have already processed
                skipped_count = 0
                if games_to_skip > 0:
                    print(f"  Skipping {games_to_skip} games...")
                    while skipped_count < games_to_skip:
                        if chess.pgn.read_game(f) is None: return 0 # End of file
                        skipped_count += 1

                # Now, load the next chunk of games for training
                while games_loaded_this_chunk < max_games_to_load:
                    try:
                        game = chess.pgn.read_game(f)
                        if game is None: break # Reached end of file

                        if 'Result' not in game.headers: continue
                        board = game.board()
                        positions = []
                        move_count = 0
                        for move in game.mainline_moves():
                            board.push(move)
                            move_count += 1
                            if move_count > 10 and len(positions) < 30:
                                board_tensor = self.data_processor.board_to_tensor(board)
                                features = self.data_processor.get_game_features(board)
                                evaluation = self.data_processor.evaluate_position(board)
                                positions.append((board_tensor, features, evaluation))

                        if positions:
                            self.training_data.extend(positions)
                            games_loaded_this_chunk += 1
                    except (ValueError, IndexError, KeyError):
                        continue # Skip malformed game
        except Exception as e:
            print(f"❌ Error during PGN chunk loading: {e}")
        return games_loaded_this_chunk

    # --- Other methods remain unchanged ---
    def get_available_checkpoints(self): pass # Unchanged
    def self_play_game(self, max_moves=100): pass # Unchanged
    def save_self_play_game(self, game, checkpoint_name): pass # Unchanged
    def plot_training_history(self): pass # Unchanged

class ChessTrainingUI(tk.Tk):
    """Interactive UI for chess training with new automated pipeline controls"""

    def __init__(self):
        super().__init__()
        self.trainer = ChessTrainer()
        self.title("CNN Chess AI Training System")
        self.geometry("800x600")
        self.setup_ui()

    def setup_ui(self):
        # --- Main Frames ---
        control_frame = ttk.Frame(self, padding="10")
        control_frame.pack(side="left", fill="y")
        output_frame = ttk.Frame(self, padding="10")
        output_frame.pack(side="right", fill="both", expand=True)

        # --- Output Text Widget ---
        self.output_text = tk.Text(output_frame, wrap="word", height=25, width=80)
        self.output_text.pack(fill="both", expand=True)
        # Redirect stdout to the text widget
        import sys
        sys.stdout = self.TextRedirector(self.output_text, "stdout")
        sys.stderr = self.TextRedirector(self.output_text, "stderr")


        # --- Setup Controls ---
        setup_frame = ttk.LabelFrame(control_frame, text="1. Setup", padding="10")
        setup_frame.pack(fill="x", pady=5)

        self.pgn_dir_var = tk.StringVar()
        self.checkpoint_dir_var = tk.StringVar()

        ttk.Button(setup_frame, text="Select PGN Directory", command=self.select_pgn_dir).pack(fill="x", pady=2)
        ttk.Label(setup_frame, textvariable=self.pgn_dir_var).pack(fill="x")
        ttk.Button(setup_frame, text="Select Checkpoint Directory", command=self.select_checkpoint_dir).pack(fill="x", pady=2)
        ttk.Label(setup_frame, textvariable=self.checkpoint_dir_var).pack(fill="x")

        # --- Automated Training Controls ---
        auto_train_frame = ttk.LabelFrame(control_frame, text="2. Automated Training", padding="10")
        auto_train_frame.pack(fill="x", pady=5)

        ttk.Label(auto_train_frame, text="Games per Chunk:").grid(row=0, column=0, sticky="w")
        self.auto_chunk_size = tk.IntVar(value=5000)
        ttk.Entry(auto_train_frame, textvariable=self.auto_chunk_size).grid(row=0, column=1)

        ttk.Label(auto_train_frame, text="Epochs per Chunk:").grid(row=1, column=0, sticky="w")
        self.auto_epochs = tk.IntVar(value=5)
        ttk.Entry(auto_train_frame, textvariable=self.auto_epochs).grid(row=1, column=1)

        ttk.Button(auto_train_frame, text="Start Automated Training", command=self.auto_train_callback).grid(row=2, columnspan=2, pady=5)


        # --- Manual Controls ---
        manual_train_frame = ttk.LabelFrame(control_frame, text="3. Manual Controls", padding="10")
        manual_train_frame.pack(fill="x", pady=5)

        ttk.Label(manual_train_frame, text="Epochs:").grid(row=0, column=0, sticky="w")
        self.epochs_input = tk.IntVar(value=10)
        ttk.Entry(manual_train_frame, textvariable=self.epochs_input).grid(row=0, column=1)

        ttk.Button(manual_train_frame, text="Start Manual Training", command=self.train_callback).grid(row=1, columnspan=2, pady=5)
        ttk.Button(manual_train_frame, text="Load PGN Data", command=self.load_data_callback).grid(row=2, columnspan=2, pady=2)


        # --- Checkpoints & Utilities ---
        checkpoint_frame = ttk.LabelFrame(control_frame, text="4. Checkpoints & Utilities", padding="10")
        checkpoint_frame.pack(fill="x", pady=5)

        ttk.Label(checkpoint_frame, text="Load checkpoint:").pack(fill="x")
        self.checkpoint_dropdown = ttk.Combobox(checkpoint_frame)
        self.checkpoint_dropdown.pack(fill="x", pady=2)
        ttk.Button(checkpoint_frame, text="Refresh List", command=self.refresh_checkpoints_callback).pack(fill="x", pady=2)
        ttk.Button(checkpoint_frame, text="Load Checkpoint", command=self.load_checkpoint_callback).pack(fill="x", pady=2)

        ttk.Label(checkpoint_frame, text="Save Name:").pack(fill="x")
        self.checkpoint_name_input = tk.StringVar()
        ttk.Entry(checkpoint_frame, textvariable=self.checkpoint_name_input).pack(fill="x", pady=2)
        ttk.Button(checkpoint_frame, text="Save Manual Checkpoint", command=self.save_checkpoint_callback).pack(fill="x", pady=2)

        ttk.Button(checkpoint_frame, text="Self-Play Game", command=self.self_play_callback).pack(fill="x", pady=5)
        ttk.Button(checkpoint_frame, text="Plot Training History", command=self.plot_callback).pack(fill="x", pady=2)


    def select_pgn_dir(self):
        dir_path = filedialog.askdirectory(title="Select PGN Directory")
        if dir_path:
            self.pgn_dir_var.set(dir_path)
            self.check_directories()

    def select_checkpoint_dir(self):
        dir_path = filedialog.askdirectory(title="Select Checkpoint Directory")
        if dir_path:
            self.checkpoint_dir_var.set(dir_path)
            self.check_directories()

    def check_directories(self):
        pgn_dir = self.pgn_dir_var.get()
        checkpoint_dir = self.checkpoint_dir_var.get()
        if pgn_dir and checkpoint_dir:
            self.trainer.set_directories(pgn_dir, checkpoint_dir)
            self.refresh_checkpoints_callback()

    def auto_train_callback(self):
        if not self.trainer.directories_set:
            messagebox.showerror("Error", "Please select PGN and Checkpoint directories first.")
            return
        chunk_size = self.auto_chunk_size.get()
        epochs = self.auto_epochs.get()
        print(f"🤖 Starting automated training pipeline...")
        print(f"   - Games per chunk: {chunk_size}")
        print(f"   - Epochs per chunk: {epochs}")
        threading.Thread(target=self.trainer.start_automated_training, args=(chunk_size, epochs), daemon=True).start()

    def load_data_callback(self):
        if not self.trainer.directories_set:
            messagebox.showerror("Error", "Please select PGN and Checkpoint directories first.")
            return
        print("📖 Loading PGN data manually...")
        # Note: This uses the original manual loading method
        threading.Thread(target=self.trainer.load_pgn_files, args=(1000,), daemon=True).start()


    def train_callback(self):
        if not self.trainer.directories_set:
            messagebox.showerror("Error", "Please select PGN and Checkpoint directories first.")
            return
        print("🚀 Starting manual training session...")
        threading.Thread(target=self.trainer.train_model, args=(self.epochs_input.get(),), daemon=True).start()


    def save_checkpoint_callback(self):
        if not self.trainer.directories_set:
            messagebox.showerror("Error", "Please select PGN and Checkpoint directories first.")
            return
        name = self.checkpoint_name_input.get() or None
        if self.trainer.save_checkpoint(name, is_auto=False):
            self.refresh_checkpoints_callback()

    def load_checkpoint_callback(self):
        if not self.trainer.directories_set:
            messagebox.showerror("Error", "Please select PGN and Checkpoint directories first.")
            return
        if self.checkpoint_dropdown.get():
            self.trainer.load_checkpoint(self.checkpoint_dropdown.get())

    def refresh_checkpoints_callback(self):
        if not self.trainer.directories_set:
            return
        try:
            checkpoints = [d for d in os.listdir(self.trainer.checkpoint_dir) if os.path.isdir(os.path.join(self.trainer.checkpoint_dir, d))]
            self.checkpoint_dropdown['values'] = checkpoints
            if checkpoints:
                self.checkpoint_dropdown.set(checkpoints[0])
        except FileNotFoundError:
            self.checkpoint_dropdown['values'] = []


    def self_play_callback(self):
        if not self.trainer.model:
            messagebox.showerror("Error", "No model loaded.")
            return
        print("♟️ Starting self-play game...")
        threading.Thread(target=self.trainer.self_play_game, daemon=True).start()


    def plot_callback(self):
        if not self.trainer.history['loss']:
            messagebox.showinfo("Info", "No training history to plot.")
            return
        plot_window = tk.Toplevel(self)
        plot_window.title("Training History")
        fig, ax = plt.subplots(figsize=(10, 5))
        sns.lineplot(data=self.trainer.history, ax=ax)
        ax.set_title("Training History")
        ax.set_xlabel("Epoch")
        ax.set_ylabel("Value")
        canvas = FigureCanvasTkAgg(fig, master=plot_window)
        canvas.draw()
        canvas.get_tk_widget().pack()


    class TextRedirector(object):
        def __init__(self, widget, tag="stdout"):
            self.widget = widget
            self.tag = tag

        def write(self, str):
            self.widget.configure(state="normal")
            self.widget.insert("end", str, (self.tag,))
            self.widget.configure(state="disabled")
            self.widget.see("end")

        def flush(self):
            pass


# --- Main execution ---
if __name__ == "__main__":
    app = ChessTrainingUI()
    app.mainloop()